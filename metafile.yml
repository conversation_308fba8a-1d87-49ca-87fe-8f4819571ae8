---
nodes:
  core:
    command: bash
    image: hb.ua.ubtrobot.com/pub/docker/isaac:isaac_sim_2023.1.1-feat-init
    network_mode: host
    shm_size: 4g
    stdin_open: true
    tty: true
    use_gpus: true
    user: root
    environment:
      - PRIVACY_CONSENT=Y
      - ACCEPT_EULA=Y
    volumes:
      - [./isaac-sim/cache/glcache, ~/.cache/nvidia/GLCache, rw]
      - [./isaac-sim/cache/pip, ~/.cache/pip, rw]
      - [./isaac-sim/cache/kit, /isaac-sim/kit/cache, rw]
      - [./isaac-sim/cache/ov, ~/.cache/ov, rw]
      - [./isaac-sim/logs, ~/.nvidia-omniverse/logs, rw]
      - [./isaac-sim/cache/computecache, ~/.nv/ComputeCache, rw]
      - [./isaac-sim/data, ~/.local/share/ov/data, rw]
      - [./isaac-sim/documents, ~/Documents, rw]
      - [./workspace, ~/workspace, rw]
      - [~/.ssh, ~/.ssh, rw]
      - [/etc/localtime, /etc/localtime, ro]
      - [/etc/timezone, /etc/timezone, ro]
    working_dir: "~"
