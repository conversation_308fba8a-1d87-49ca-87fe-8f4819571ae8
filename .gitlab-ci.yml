include:
  - project: "pub/ci_templates"
    file: "/docker.build/define.gitlab-ci.yml"

stages:
  - build

.matrix:
  parallel:
    matrix:
      - SRC_TAG: isaac_sim_5.0.0
        TAG: isaac_sim_5.0.0
        BASE_IMAGE_NAME: glcr.rd.ubtrobot.com/pub/nvcr.io/nvidia/isaac-sim:5.0.0

build:ubuntu22_merge_requests:
  variables:
    PLATFORM: linux/amd64
  extends:
    - .merge_requests_job
    - .matrix


build:ubuntu22_main:
  variables:
    PLATFORM: linux/amd64
  extends:
    - .main_job
    - .matrix
  rules:
    - if: $CI_COMMIT_BRANCH == "isaac-5.0"

