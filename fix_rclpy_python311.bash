#!/bin/bash

# 修复rclpy Python 3.11兼容性问题的脚本

set -e

# 设置变量
ISAAC_PYTHON_PATH=/opt/isaac-sim/kit/python
ROS_INSTALL_PATH=/opt/ros/humble
ROS_ROOT=/opt/src/ros_ws

echo "开始修复rclpy Python 3.11兼容性问题..."

# 1. 设置环境变量
export PATH="${ISAAC_PYTHON_PATH}/bin:$PATH"
export PYTHONPATH="${ISAAC_PYTHON_PATH}/lib/python3.11/site-packages:$PYTHONPATH"
export LD_LIBRARY_PATH="${ISAAC_PYTHON_PATH}/lib:$LD_LIBRARY_PATH"

# 2. 检查当前Python版本
echo "当前Python版本: $(python3 --version)"
echo "当前Python路径: $(which python3)"

# 3. 检查是否存在rclpy源码
if [ ! -d "${ROS_ROOT}/src/rclpy" ]; then
    echo "错误: 找不到rclpy源码目录 ${ROS_ROOT}/src/rclpy"
    echo "请确保已经运行过ros.bash脚本构建ROS2"
    exit 1
fi

# 4. 重新构建rclpy包
echo "重新构建rclpy包以支持Python 3.11..."
cd ${ROS_ROOT}

# 清理之前的构建
rm -rf build/rclpy install/lib/python3.11/site-packages/rclpy* || true
rm -rf ${ROS_INSTALL_PATH}/lib/python3.11/site-packages/rclpy* || true

# 重新构建rclpy
colcon build --packages-select rclpy --cmake-args \
    "-DPython3_EXECUTABLE=${ISAAC_PYTHON_PATH}/bin/python3.11" \
    "-DPYTHON_EXECUTABLE=${ISAAC_PYTHON_PATH}/bin/python3.11" \
    "-DPYTHON_INCLUDE_DIR=${ISAAC_PYTHON_PATH}/include/python3.11" \
    "-DPYTHON_LIBRARY=${ISAAC_PYTHON_PATH}/lib/libpython3.11.so" \
    "-DCMAKE_BUILD_TYPE=Release" \
    --merge-install \
    --install-base ${ROS_INSTALL_PATH}

# 5. 验证构建结果
echo "验证rclpy构建结果..."
RCLPY_SO_FILE="${ROS_INSTALL_PATH}/lib/python3.11/site-packages/_rclpy_pybind11.cpython-311-x86_64-linux-gnu.so"

if [ -f "$RCLPY_SO_FILE" ]; then
    echo "✓ 找到Python 3.11兼容的rclpy C扩展: $RCLPY_SO_FILE"
    echo "文件信息:"
    ls -la "$RCLPY_SO_FILE"
    echo "依赖库检查:"
    ldd "$RCLPY_SO_FILE" | head -10
else
    echo "✗ 未找到Python 3.11兼容的rclpy C扩展"
    echo "查找所有rclpy相关的.so文件:"
    find ${ROS_INSTALL_PATH} -name "*rclpy*" -name "*.so" 2>/dev/null || true
fi

# 6. 创建测试脚本
cat > test_rclpy.py << 'EOF'
#!/usr/bin/env python3
import sys
print(f"Python版本: {sys.version}")
print(f"Python路径: {sys.executable}")

try:
    import rclpy
    print("✓ 成功导入rclpy")
    
    # 测试rclpy初始化
    rclpy.init()
    print("✓ rclpy初始化成功")
    rclpy.shutdown()
    print("✓ rclpy关闭成功")
    
    print("rclpy测试通过!")
except Exception as e:
    print(f"✗ rclpy测试失败: {e}")
    import traceback
    traceback.print_exc()
EOF

# 7. 运行测试
echo "运行rclpy测试..."
${ISAAC_PYTHON_PATH}/bin/python3.11 test_rclpy.py

echo "修复完成!"
echo ""
echo "使用方法："
echo "1. source setup_ros2_python311.bash"
echo "2. 使用ros2命令"
