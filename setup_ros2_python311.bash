#!/bin/bash

# ROS2 Python 3.11 环境设置脚本
# 解决Isaac Sim Python 3.11与ROS2 Python版本混合问题

# Isaac Sim Python 3.11路径
ISAAC_PYTHON_PATH=/opt/isaac-sim/kit/python
ROS_INSTALL_PATH=/opt/ros/humble

echo "设置ROS2 Python 3.11环境..."

# 1. 设置Python路径优先级，确保使用Isaac Sim的Python 3.11
export PATH="${ISAAC_PYTHON_PATH}/bin:$PATH"

# 2. 设置PYTHONPATH，确保Python包路径正确
export PYTHONPATH="${ISAAC_PYTHON_PATH}/lib/python3.11/site-packages:${ROS_INSTALL_PATH}/lib/python3.11/site-packages:$PYTHONPATH"

# 3. 设置库路径
export LD_LIBRARY_PATH="${ISAAC_PYTHON_PATH}/lib:${ROS_INSTALL_PATH}/lib:$LD_LIBRARY_PATH"

# 4. 设置ROS2环境变量
source ${ROS_INSTALL_PATH}/setup.bash

# 5. 验证Python版本
echo "当前Python版本: $(python3 --version)"
echo "当前Python路径: $(which python3)"

# 6. 创建ros2命令别名，强制使用Python 3.11
alias ros2='${ISAAC_PYTHON_PATH}/bin/python3.11 ${ROS_INSTALL_PATH}/bin/ros2'

# 7. 验证ROS2环境
echo "ROS2环境设置完成"
echo "PYTHONPATH: $PYTHONPATH"
echo "LD_LIBRARY_PATH: $LD_LIBRARY_PATH"

# 8. 测试函数
test_ros2() {
    echo "测试ROS2命令..."
    echo "运行: ros2 --help"
    ros2 --help
}

echo "使用方法："
echo "1. source setup_ros2_python311.bash"
echo "2. 运行 test_ros2 测试ROS2命令"
echo "3. 正常使用ros2命令"
