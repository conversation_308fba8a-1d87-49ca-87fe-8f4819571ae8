ARG BASE_IMAGE=glcr.rd.ubtrobot.com/pub/nvcr.io/nvidia/isaac-sim:5.0.0
FROM $BASE_IMAGE
ARG TARGETPLATFORM

USER root

SHELL ["/bin/bash", "-o", "pipefail", "-c"]

ARG USER=ubt

COPY base /base
WORKDIR /base
RUN ./base.bash ${USER} && rm -rf /base

# 安装 zsh
RUN sudo apt update && \
    sudo apt install -y curl gnupg2 zsh ssh git

# 安装 fzf
RUN bash -c "$(curl -fsSL http://store.ua.ubtrobot.com/d/public/software/fzf/install.sh)" && command -v fzf

USER ${USER}
WORKDIR /home/<USER>

# 在 user 下配置主机密钥
RUN mkdir -p -m 0700 ~/.ssh && ssh-keyscan -t ecdsa gitlab.rd.ubtrobot.com >> ~/.ssh/known_hosts
RUN mkdir -p -m 0700 ~/.ssh && ssh-keyscan -t ecdsa gh-proxy.rd.ubtrobot.com >> ~/.ssh/known_hosts

RUN <<EOF
    git clone http://gh-proxy.rd.ubtrobot.com/ohmyzsh/ohmyzsh.git
    cd ohmyzsh/tools
    REMOTE=http://gh-proxy.rd.ubtrobot.com/ohmyzsh/ohmyzsh.git sh install.sh
    cd ../../
    rm -rf ohmyzsh
    # 插件
    git clone http://gh-proxy.rd.ubtrobot.com/zsh-users/zsh-autosuggestions.git ${ZSH_CUSTOM:-~/.oh-my-zsh/custom}/plugins/zsh-autosuggestions
    git clone http://gh-proxy.rd.ubtrobot.com/zdharma-continuum/fast-syntax-highlighting.git ${ZSH_CUSTOM:-~/.oh-my-zsh/custom}/plugins/fast-syntax-highlighting
EOF

RUN sed -i 's/plugins=(git)/plugins=(git z sudo zsh-autosuggestions fast-syntax-highlighting)/g' ~/.zshrc

RUN echo '# fzf' >> ~/.bashrc
RUN echo 'eval "$(fzf --bash)"' >> ~/.bashrc
RUN echo '# fzf' >> ~/.zshrc
RUN echo 'source <(fzf --zsh)' >> ~/.zshrc

# 为了让正常显示中文编码
ENV LANG=en_US.UTF-8
ENV LC_ALL=en_US.UTF-8

# isaac-sim
USER root
RUN mkdir -p /opt/isaac-sim && cp -a /isaac-sim /opt/ && chown -R ${USER}:${USER} /opt/isaac-sim && rm -rf /isaac-sim
ENV PATH="/opt/isaac-sim/kit/python/bin:$PATH"

USER root
COPY ros/depend.bash /tmp/depend.bash
RUN /tmp/depend.bash ${USER}
COPY ros/ros.bash /tmp/ros.bash
RUN /tmp/ros.bash ${USER}

COPY isaac_ros_src /opt/src/isaac_ros_src
RUN ls /opt/src/isaac_ros_src
RUN /opt/src/isaac_ros_src/isaac_ros.bash ${USER}
# RUN sudo mkdir -p /opt/src && chown -R ${USER}:${USER} /opt/src

# # 编译 ros2
# ENV ROS_DISTRO=humble
# ENV ROS_ROOT=ros_ws
# ENV ISAAC_ROS_ROOT=isaac_ros_ws
# ENV ROS_PYTHON_VERSION=3
# ENV DEBIAN_FRONTEND=noninteractive


# WORKDIR /opt/src

# # USER root

# RUN apt-get update && \
#     apt-get install -y --no-install-recommends \
#         git cmake build-essential curl wget gnupg2 lsb-release software-properties-common locales

# RUN locale-gen en_US en_US.UTF-8 && \
#     update-locale LC_ALL=en_US.UTF-8 LANG=en_US.UTF-8 && \
#     export LANG=en_US.UTF-8

# RUN wget https://raw.githubusercontent.com/ros/rosdistro/master/ros.asc && apt-key add ros.asc && \
#     sh -c 'echo "deb [arch=$(dpkg --print-architecture)] http://packages.ros.org/ros2/ubuntu $(lsb_release -cs) main" > /etc/apt/sources.list.d/ros2-latest.list'

# # Additional dependencies needed for rosidl_generator_c
# RUN apt update && apt install -y \
#     pkg-config \
#     python3-yaml \
#     cmake-extras
# # Install Boost libraries needed for OMPL
# RUN apt update && apt install -y \
#     libboost-all-dev \
#     libboost-dev \
#     libboost-filesystem-dev \
#     libboost-program-options-dev \
#     libboost-system-dev \
#     libboost-thread-dev \
#     libboost-serialization-dev \
#     libboost-date-time-dev \
#     libboost-regex-dev \
#     libboost-python-dev \
#     libfmt-dev
# # Install dependencies for geometric_shapes and other packages
# RUN apt update && apt install -y \
#     libqhull-dev \
#     libassimp-dev \
#     liboctomap-dev \
#     libconsole-bridge-dev \
#     libfcl-dev
# # Install Eigen3 needed for OMPL and MoveIt
# RUN apt update && apt install -y \
#     libeigen3-dev
# # Install X11 and graphics dependencies needed for OGRE (RViz)
# RUN apt update && apt install -y \
#     libx11-dev \
#     libxaw7-dev \
#     libxrandr-dev \
#     libgl1-mesa-dev \
#     libglu1-mesa-dev \
#     libglew-dev \
#     libgles2-mesa-dev \
#     libopengl-dev
#     # libfreetype-dev \
#     # libfreetype6-dev \
#     # libfontconfig1-dev
# # Install Qt5 and additional dependencies for RViz
# RUN apt update && apt install -y \
#     qtbase5-dev \
#     qtchooser \
#     qt5-qmake \
#     qtbase5-dev-tools \
#     libqt5core5a \
#     libqt5gui5 \
#     libqt5opengl5 \
#     libqt5widgets5 \
#     libxcursor-dev \
#     libxinerama-dev \
#     libxi-dev \
#     libyaml-cpp-dev \
#     libassimp-dev \
#     libzzip-dev \
#     freeglut3-dev \
#     libogre-1.9-dev \
#     libpng-dev \
#     libjpeg-dev \
#     python3-pyqt5.qtwebengine

# RUN apt update && apt install -y \
#     ros-dev-tools \
#     libbullet-dev \
#     libasio-dev \
#     libtinyxml2-dev \
#     libcunit1-dev \
#     libacl1-dev

# RUN python3.11 -m pip install empy==3.3.4
# RUN python3 -m pip install -U \
#   argcomplete \
#   flake8-blind-except \
#   flake8-builtins \
#   flake8-class-newline \
#   flake8-comprehensions \
#   flake8-deprecated \
#   flake8-docstrings \
#   flake8-import-order \
#   flake8-quotes \
#   pytest-repeat \
#   pytest-rerunfailures \
#   pytest \
#   lark

# RUN python3.11 -m pip install numpy pybind11 PyYAML
# RUN python3.11 -m pip install "pybind11[global]"
# RUN python3.11 -m pip install rosinstall_generator

# RUN mkdir -p ${ROS_ROOT}/src && \
#     cd ${ROS_ROOT} && \
#     rosinstall_generator --deps --rosdistro ${ROS_DISTRO} rosidl_runtime_c rcutils rcl rmw tf2 tf2_msgs common_interfaces geometry_msgs nav_msgs std_msgs rosgraph_msgs sensor_msgs vision_msgs rclpy ros2topic ros2pkg ros2doctor ros2run ros2node ros_environment ackermann_msgs example_interfaces tf2_ros_py tf2_ros > ros2.${ROS_DISTRO}.${ROS_PKG}.rosinstall && \
#     sed -i 's|https://github\.com|https://gh-proxy.rd.ubtrobot.com|g' ros2.${ROS_DISTRO}.${ROS_PKG}.rosinstall && \
#     cat ros2.${ROS_DISTRO}.${ROS_PKG}.rosinstall && \
#     vcs import src < ros2.${ROS_DISTRO}.${ROS_PKG}.rosinstall

# # Patch rclpy to ensure it builds with Python 3.11 - find the correct path first
# RUN find /workspace/${ROS_ROOT}/src -name rclpy -type d | xargs -I{} /bin/bash -c 'if [ -f {}/CMakeLists.txt ]; then \
#     echo "Patching {}/CMakeLists.txt"; \
#     sed -i "s/include_directories(\${PYTHON_INCLUDE_DIRS})/include_directories(\/isaac-sim\/kit\/python\/include\/python3.11)/" {}/CMakeLists.txt; \
#     sed -i "s/\${PYTHON_LIBRARY}/python3.11/" {}/CMakeLists.txt; \
#     fi'

# # 模拟 rosdep
# ENV ROSDISTRO_INDEX_URL=https://mirrors.ustc.edu.cn/rosdistro/index-v4.yaml
# RUN mkdir -p /etc/ros/rosdep/sources.list.d && \
#     curl -o /etc/ros/rosdep/sources.list.d/20-default.list https://mirrors.ustc.edu.cn/rosdistro/rosdep/sources.list.d/20-default.list && \
#     sed -i 's#raw.githubusercontent.com/ros/rosdistro/master#mirrors.ustc.edu.cn/rosdistro#g' /etc/ros/rosdep/sources.list.d/20-default.list && \
#     rosdep update

ENV PYTHONPATH=/opt/isaac-sim/kit/python/lib/python3.11/site-packages
    
# RUN git config --global url."http://gh-proxy.rd.ubtrobot.com".insteadOf https://github.com
# RUN sed -i 's|URL https://github.com/gabime/spdlog/archive/v1.8.2.tar.gz|GIT_REPOSITORY https://gh-proxy.rd.ubtrobot.com/gabime/spdlog.git|; s|URL_MD5 22518fb28d4be66c92a703c67d99b1d1|GIT_TAG v1.8.2|' ${ROS_ROOT}/src/spdlog_vendor/CMakeLists.txt

# # Use logging to help debug build issues
# RUN cd ${ROS_ROOT} && colcon build --cmake-args \
#     "-DPython3_EXECUTABLE=/isaac-sim/kit/python/bin/python3.11" \
#     "-DPYTHON_EXECUTABLE=/isaac-sim/kit/python/bin/python3.11" \
#     "-DPYTHON_INCLUDE_DIR=/isaac-sim/kit/python/include/python3.11" \
#     "-DPYTHON_LIBRARY=/isaac-sim/kit/python/lib/libpython3.11.so" \
#     --merge-install

# # Need these to maintain compatibility on non 20.04 systems
# RUN cp /usr/lib/x86_64-linux-gnu/libtinyxml2.so* /workspace/${ROS_ROOT}/install/lib/ || true
# RUN cp /usr/lib/x86_64-linux-gnu/libssl.so* /workspace/${ROS_ROOT}/install/lib/ || true
# RUN cp /usr/lib/x86_64-linux-gnu/libcrypto.so* /workspace/${ROS_ROOT}/install/lib/ || true

# # Next, build the additional workspace 
# RUN mkdir -p /workspace/${ISAAC_ROS_ROOT}/src
# COPY src /workspace/${ISAAC_ROS_ROOT}/src

# # Removing MoveIt packages from the internal ROS Python 3.11 library build as it uses standard interfaces already built above.
# # This is to ensure that the internal build is as minimal as possible. 
# # For the user facing MoveIt interface workflow, this package should be built with the rest of the workspace uisng the external ROS installation.
# RUN rm -rf /workspace/${ISAAC_ROS_ROOT}/src/moveit

# WORKDIR /workspace
# # Build the workspace with Python 3.11
# RUN /bin/bash -c "source ${ROS_ROOT}/install/setup.sh && cd ${ISAAC_ROS_ROOT} && colcon build --cmake-args \
#     '-DPython3_EXECUTABLE=/isaac-sim/kit/python/bin/python3.11' \
#     '-DPYTHON_EXECUTABLE=/isaac-sim/kit/python/bin/python3.11' \
#     '-DPYTHON_INCLUDE_DIR=/isaac-sim/kit/python/include/python3.11' \
#     '-DPYTHON_LIBRARY=/isaac-sim/kit/python/lib/libpython3.11.so'"

# USER ubt
# WORKDIR /home/<USER>

# RUN <<EOF
#     git clone http://gh-proxy.rd.ubtrobot.com/ohmyzsh/ohmyzsh.git
#     cd ohmyzsh/tools
#     REMOTE=http://gh-proxy.rd.ubtrobot.com/ohmyzsh/ohmyzsh.git sh install.sh
#     cd ../../
#     rm -rf ohmyzsh
#     # 插件
#     git clone http://gh-proxy.rd.ubtrobot.com/zsh-users/zsh-autosuggestions.git ${ZSH_CUSTOM:-~/.oh-my-zsh/custom}/plugins/zsh-autosuggestions
#     git clone http://gh-proxy.rd.ubtrobot.com/zdharma-continuum/fast-syntax-highlighting.git ${ZSH_CUSTOM:-~/.oh-my-zsh/custom}/plugins/fast-syntax-highlighting
# EOF

# RUN sed -i 's/plugins=(git)/plugins=(git z sudo zsh-autosuggestions fast-syntax-highlighting)/g' ~/.zshrc

# RUN echo '# fzf' >> ~/.bashrc
# RUN echo 'eval "$(fzf --bash)"' >> ~/.bashrc

# RUN echo '# fzf' >> ~/.zshrc
# RUN echo 'source <(fzf --zsh)' >> ~/.zshrc

# RUN sudo chown -R ubt:ubt /isaac-sim /workspace && \
#     echo 'alias source_ros="export LD_LIBRARY_PATH=$LD_LIBRARY_PATH:/isaac-sim/exts/isaacsim.ros2.bridge/humble/lib"' >> ~/.bashrc && \
#     echo 'alias source_ros="export LD_LIBRARY_PATH=$LD_LIBRARY_PATH:/isaac-sim/exts/isaacsim.ros2.bridge/humble/lib"' >> ~/.zshrc

