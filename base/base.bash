#!/bin/bash

set -e

USER=${1:-"ubt"}

# 设置 bashrc 颜色
sed -i 's/#force_color_prompt=yes/force_color_prompt=yes/' /etc/skel/.bashrc

# 删除 ubuntu 用户 (ubuntu 24.04 需要)
cat /etc/passwd | grep -q ubuntu && userdel -r ubuntu || true

# 增加 ubt 用户
useradd --create-home --user-group --groups sudo --shell /bin/bash "$USER"

# 校验 ubt 的权限是不是 1000:1000
[ $(id -u ${USER}) == "1000" ] && [ $(id -g ${USER}) == "1000" ]
echo "ubt id ${USER} is $(id -u ${USER}):$(id -g ${USER})"

cp nvidia-repo-pin /etc/apt/preferences.d/nvidia-repo-pin

# 换 apt 源
# 文件列表 ubuntu20.04,ubuntu22.04 ubuntu24.04 debian12
files=("/etc/apt/sources.list" "/etc/apt/sources.list.d/ubuntu.sources" "/etc/apt/sources.list.d/debian.sources")

update() {
    echo "update $1"
    if [ -f "$1" ]; then
        # amd64
        sed -i 's|archive.ubuntu.com/ubuntu|nexus.rd.ubtrobot.com/repository/ubuntu-proxy|g' "$1"
        sed -i 's|security.ubuntu.com/ubuntu|nexus.rd.ubtrobot.com/repository/ubuntu-proxy|g' "$1"
        # arm64
        sed -i 's|ports.ubuntu.com/ubuntu|nexus.rd.ubtrobot.com/repository/ubuntu-proxy|g' "$1"
        sed -i 's|repo.download.nvidia.com/jetson/|nexus.rd.ubtrobot.com/repository/jetson-|g' "$1"
        # debian
        sed -i 's|deb.debian.org/debian|nexus.rd.ubtrobot.com/repository/debian-proxy|g' "$1"
    fi
}
# 循环遍历文件列表
for file in "${files[@]}"
do
    if [ -f "$file" ]; then
        update "$file"
    fi
done

# 关闭交互式配置
echo 'debconf debconf/frontend select Noninteractive' | debconf-set-selections

# 系统软件
apt update && apt install -y --no-install-recommends \
    ca-certificates \
    gnupg2 \
    locales \
    sudo

# 设置 enlish locale
echo "en_US.UTF-8 UTF-8" >> /etc/locale.gen && locale-gen

cp ubt.asc /etc/apt/keyrings/ubt.asc

ARCH=$(dpkg --print-architecture) && \
    CODENAME=$(grep '^VERSION_CODENAME=' /etc/os-release | cut -d= -f2 | tr -d '"') && \
    echo "deb [arch=${ARCH} signed-by=/etc/apt/keyrings/ubt.asc] http://ua.ubtrobot.com/repository/${CODENAME}/ ${CODENAME} main" \
    > /etc/apt/sources.list.d/ubt.list

# 添加用户组权限
usermod -aG adm,dialout,cdrom,floppy,sudo,audio,dip,video,plugdev ${USER}

# pip 源
cp pip.ubt.conf /etc/pip.conf

# 更新并清理缓存
apt update && apt clean
