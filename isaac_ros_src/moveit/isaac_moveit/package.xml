<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format3.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>isaac_moveit</name>
  <version>0.1.0</version>
  <description>
    The isaac_moveit package
  </description>

  <maintainer email="<EMAIL>"><PERSON></maintainer>

  <license>
    SPDX-FileCopyrightText: Copyright (c) 2025 NVIDIA CORPORATION &amp; AFFILIATES. All rights reserved.
    SPDX-License-Identifier: Apache-2.0

    Licensed under the Apache License, Version 2.0 (the "License");
    you may not use this file except in compliance with the License.
    You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.
  </license>
  
  <url type="Documentation">https://docs.isaacsim.omniverse.nvidia.com/5.0.0/index.html</url>
  <url type="Forums">https://forums.developer.nvidia.com/c/omniverse/simulation/69</url>

  <buildtool_depend>ament_cmake</buildtool_depend>
  <build_depend>moveit</build_depend>
  <build_depend>robot_state_publisher</build_depend>
  <build_depend>moveit_resources_panda_moveit_config</build_depend>
  <build_depend>topic_based_ros2_control</build_depend>
  <build_depend>ros2_control</build_depend>
  <build_depend>ros2_controllers</build_depend>
  <build_depend>moveit_simple_controller_manager</build_depend>
  <build_depend>visualization_msgs</build_depend>
  

  <exec_depend>launch</exec_depend>
  <exec_depend>launch_ros</exec_depend>

  <exec_depend>rviz2</exec_depend>


  <export>
    <build_type>ament_cmake</build_type>
  </export>
</package>
