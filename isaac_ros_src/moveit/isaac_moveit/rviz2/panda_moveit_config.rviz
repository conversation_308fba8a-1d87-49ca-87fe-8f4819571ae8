Panels:
  - Class: rviz_common/Displays
    Help Height: 78
    Name: Displays
    Property Tree Widget:
      Expanded: ~
      Splitter Ratio: 0.5
    Tree Height: 359
  - Class: rviz_common/Selection
    Name: Selection
  - Class: rviz_common/Tool Properties
    Expanded:
      - /2D Goal Pose1
      - /Publish Point1
    Name: Tool Properties
    Splitter Ratio: 0.5886790156364441
  - Class: rviz_common/Views
    Expanded:
      - /Current View1
      - /Current View1/Focal Point1
    Name: Views
    Splitter Ratio: 0.5
Visualization Manager:
  Class: ""
  Displays:
    - Alpha: 0.5
      Cell Size: 1
      Class: rviz_default_plugins/Grid
      Color: 160; 160; 164
      Enabled: true
      Line Style:
        Line Width: 0.029999999329447746
        Value: Lines
      Name: Grid
      Normal Cell Count: 0
      Offset:
        X: 0
        Y: 0
        Z: 0
      Plane: XY
      Plane Cell Count: 10
      Reference Frame: <Fixed Frame>
      Value: true
    - Class: rviz_default_plugins/MarkerArray
      Enabled: true
      Name: MarkerArray
      Namespaces:
        {}
      Topic:
        Depth: 100
        Durability Policy: Volatile
        History Policy: Keep Last
        Reliability Policy: Reliable
        Value: /rviz_visual_tools
      Value: true
    - Class: moveit_rviz_plugin/Trajectory
      Color Enabled: false
      Enabled: true
      Interrupt Display: false
      Links:
        All Links Enabled: true
        Expand Joint Details: false
        Expand Link Details: false
        Expand Tree: false
        Link Tree Style: Links in Alphabetic Order
        panda_hand:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        panda_leftfinger:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        panda_link0:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        panda_link1:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        panda_link2:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        panda_link3:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        panda_link4:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        panda_link5:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        panda_link6:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        panda_link7:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
        panda_link8:
          Alpha: 1
          Show Axes: false
          Show Trail: false
        panda_rightfinger:
          Alpha: 1
          Show Axes: false
          Show Trail: false
          Value: true
      Loop Animation: false
      Name: Trajectory
      Robot Alpha: 0.5
      Robot Color: 150; 50; 150
      Robot Description: robot_description
      Show Robot Collision: false
      Show Robot Visual: true
      Show Trail: false
      State Display Time: 0.05 s
      Trail Step Size: 1
      Trajectory Topic: /display_planned_path
      Use Sim Time: false
      Value: true
    - Class: moveit_rviz_plugin/PlanningScene
      Enabled: true
      Move Group Namespace: ""
      Name: PlanningScene
      Planning Scene Topic: /monitored_planning_scene
      Robot Description: robot_description
      Scene Geometry:
        Scene Alpha: 0.8999999761581421
        Scene Color: 50; 230; 50
        Scene Display Time: 0.009999999776482582
        Show Scene Geometry: true
        Voxel Coloring: Z-Axis
        Voxel Rendering: Occupied Voxels
      Scene Robot:
        Attached Body Color: 150; 50; 150
        Links:
          All Links Enabled: true
          Expand Joint Details: false
          Expand Link Details: false
          Expand Tree: false
          Link Tree Style: Links in Alphabetic Order
          panda_hand:
            Alpha: 1
            Show Axes: false
            Show Trail: false
            Value: true
          panda_leftfinger:
            Alpha: 1
            Show Axes: false
            Show Trail: false
            Value: true
          panda_link0:
            Alpha: 1
            Show Axes: false
            Show Trail: false
            Value: true
          panda_link1:
            Alpha: 1
            Show Axes: false
            Show Trail: false
            Value: true
          panda_link2:
            Alpha: 1
            Show Axes: false
            Show Trail: false
            Value: true
          panda_link3:
            Alpha: 1
            Show Axes: false
            Show Trail: false
            Value: true
          panda_link4:
            Alpha: 1
            Show Axes: false
            Show Trail: false
            Value: true
          panda_link5:
            Alpha: 1
            Show Axes: false
            Show Trail: false
            Value: true
          panda_link6:
            Alpha: 1
            Show Axes: false
            Show Trail: false
            Value: true
          panda_link7:
            Alpha: 1
            Show Axes: false
            Show Trail: false
            Value: true
          panda_link8:
            Alpha: 1
            Show Axes: false
            Show Trail: false
          panda_rightfinger:
            Alpha: 1
            Show Axes: false
            Show Trail: false
            Value: true
        Robot Alpha: 1
        Show Robot Collision: false
        Show Robot Visual: true
      Value: true
    - Acceleration_Scaling_Factor: 0.1
      Class: moveit_rviz_plugin/MotionPlanning
      Enabled: true
      Move Group Namespace: ""
      MoveIt_Allow_Approximate_IK: false
      MoveIt_Allow_External_Program: false
      MoveIt_Allow_Replanning: false
      MoveIt_Allow_Sensor_Positioning: false
      MoveIt_Planning_Attempts: 10
      MoveIt_Planning_Time: 5
      MoveIt_Use_Cartesian_Path: false
      MoveIt_Use_Constraint_Aware_IK: false
      MoveIt_Workspace:
        Center:
          X: 0
          Y: 0
          Z: 0
        Size:
          X: 2
          Y: 2
          Z: 2
      Name: MotionPlanning
      Planned Path:
        Color Enabled: false
        Interrupt Display: false
        Links:
          All Links Enabled: true
          Expand Joint Details: false
          Expand Link Details: false
          Expand Tree: false
          Link Tree Style: Links in Alphabetic Order
          panda_hand:
            Alpha: 1
            Show Axes: false
            Show Trail: false
            Value: true
          panda_leftfinger:
            Alpha: 1
            Show Axes: false
            Show Trail: false
            Value: true
          panda_link0:
            Alpha: 1
            Show Axes: false
            Show Trail: false
            Value: true
          panda_link1:
            Alpha: 1
            Show Axes: false
            Show Trail: false
            Value: true
          panda_link2:
            Alpha: 1
            Show Axes: false
            Show Trail: false
            Value: true
          panda_link3:
            Alpha: 1
            Show Axes: false
            Show Trail: false
            Value: true
          panda_link4:
            Alpha: 1
            Show Axes: false
            Show Trail: false
            Value: true
          panda_link5:
            Alpha: 1
            Show Axes: false
            Show Trail: false
            Value: true
          panda_link6:
            Alpha: 1
            Show Axes: false
            Show Trail: false
            Value: true
          panda_link7:
            Alpha: 1
            Show Axes: false
            Show Trail: false
            Value: true
          panda_link8:
            Alpha: 1
            Show Axes: false
            Show Trail: false
          panda_rightfinger:
            Alpha: 1
            Show Axes: false
            Show Trail: false
            Value: true
        Loop Animation: false
        Robot Alpha: 0.5
        Robot Color: 150; 50; 150
        Show Robot Collision: false
        Show Robot Visual: true
        Show Trail: false
        State Display Time: 3x
        Trail Step Size: 1
        Trajectory Topic: /display_planned_path
        Use Sim Time: false
      Planning Metrics:
        Payload: 1
        Show Joint Torques: false
        Show Manipulability: false
        Show Manipulability Index: false
        Show Weight Limit: false
        TextHeight: 0.07999999821186066
      Planning Request:
        Colliding Link Color: 255; 0; 0
        Goal State Alpha: 1
        Goal State Color: 250; 128; 0
        Interactive Marker Size: 0
        Joint Violation Color: 255; 0; 255
        Planning Group: panda_arm
        Query Goal State: true
        Query Start State: false
        Show Workspace: false
        Start State Alpha: 1
        Start State Color: 0; 255; 0
      Planning Scene Topic: /monitored_planning_scene
      Robot Description: robot_description
      Scene Geometry:
        Scene Alpha: 0.8999999761581421
        Scene Color: 50; 230; 50
        Scene Display Time: 0.009999999776482582
        Show Scene Geometry: true
        Voxel Coloring: Z-Axis
        Voxel Rendering: Occupied Voxels
      Scene Robot:
        Attached Body Color: 150; 50; 150
        Links:
          All Links Enabled: true
          Expand Joint Details: false
          Expand Link Details: false
          Expand Tree: false
          Link Tree Style: Links in Alphabetic Order
          panda_hand:
            Alpha: 1
            Show Axes: false
            Show Trail: false
            Value: true
          panda_leftfinger:
            Alpha: 1
            Show Axes: false
            Show Trail: false
            Value: true
          panda_link0:
            Alpha: 1
            Show Axes: false
            Show Trail: false
            Value: true
          panda_link1:
            Alpha: 1
            Show Axes: false
            Show Trail: false
            Value: true
          panda_link2:
            Alpha: 1
            Show Axes: false
            Show Trail: false
            Value: true
          panda_link3:
            Alpha: 1
            Show Axes: false
            Show Trail: false
            Value: true
          panda_link4:
            Alpha: 1
            Show Axes: false
            Show Trail: false
            Value: true
          panda_link5:
            Alpha: 1
            Show Axes: false
            Show Trail: false
            Value: true
          panda_link6:
            Alpha: 1
            Show Axes: false
            Show Trail: false
            Value: true
          panda_link7:
            Alpha: 1
            Show Axes: false
            Show Trail: false
            Value: true
          panda_link8:
            Alpha: 1
            Show Axes: false
            Show Trail: false
          panda_rightfinger:
            Alpha: 1
            Show Axes: false
            Show Trail: false
            Value: true
        Robot Alpha: 1
        Show Robot Collision: false
        Show Robot Visual: true
      Value: true
      Velocity_Scaling_Factor: 0.1
    - Class: rviz_default_plugins/MarkerArray
      Enabled: true
      Name: MarkerArray
      Namespaces:
        cubes: true
      Topic:
        Depth: 5
        Durability Policy: Volatile
        History Policy: Keep Last
        Reliability Policy: Reliable
        Value: /visualization_marker_array
      Value: true
  Enabled: true
  Global Options:
    Background Color: 48; 48; 48
    Fixed Frame: world
    Frame Rate: 30
  Name: root
  Tools:
    - Class: rviz_default_plugins/Interact
      Hide Inactive Objects: true
    - Class: rviz_default_plugins/MoveCamera
    - Class: rviz_default_plugins/Select
    - Class: rviz_default_plugins/FocusCamera
    - Class: rviz_default_plugins/Measure
      Line color: 128; 128; 0
    - Class: rviz_default_plugins/SetInitialPose
      Covariance x: 0.25
      Covariance y: 0.25
      Covariance yaw: 0.06853891909122467
      Topic:
        Depth: 5
        Durability Policy: Volatile
        History Policy: Keep Last
        Reliability Policy: Reliable
        Value: /initialpose
    - Class: rviz_default_plugins/SetGoal
      Topic:
        Depth: 5
        Durability Policy: Volatile
        History Policy: Keep Last
        Reliability Policy: Reliable
        Value: /goal_pose
    - Class: rviz_default_plugins/PublishPoint
      Single click: true
      Topic:
        Depth: 5
        Durability Policy: Volatile
        History Policy: Keep Last
        Reliability Policy: Reliable
        Value: /clicked_point
  Transformation:
    Current:
      Class: rviz_default_plugins/TF
  Value: true
  Views:
    Current:
      Class: rviz_default_plugins/Orbit
      Distance: 3.0075223445892334
      Enable Stereo Rendering:
        Stereo Eye Separation: 0.05999999865889549
        Stereo Focal Distance: 1
        Swap Stereo Eyes: false
        Value: false
      Focal Point:
        X: -0.43977656960487366
        Y: 0.008697107434272766
        Z: 0.054967161267995834
      Focal Shape Fixed Size: true
      Focal Shape Size: 0.05000000074505806
      Invert Z Axis: false
      Name: Current View
      Near Clip Distance: 0.009999999776482582
      Pitch: 0.6397961974143982
      Target Frame: panda_link0
      Value: Orbit (rviz_default_plugins)
      Yaw: 0.5386636853218079
    Saved: ~
Window Geometry:
  Displays:
    collapsed: false
  Height: 1043
  Hide Left Dock: false
  Hide Right Dock: false
  MotionPlanning:
    collapsed: false
  MotionPlanning - Trajectory Slider:
    collapsed: false
  QMainWindow State: 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
  Selection:
    collapsed: false
  Tool Properties:
    collapsed: false
  Trajectory - Trajectory Slider:
    collapsed: false
  Views:
    collapsed: false
  Width: 1920
  X: 1426
  Y: 75
