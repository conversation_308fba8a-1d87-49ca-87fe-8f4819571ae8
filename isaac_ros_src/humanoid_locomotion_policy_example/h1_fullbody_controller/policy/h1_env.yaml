viewer:
  eye: !!python/tuple
  - 7.5
  - 7.5
  - 7.5
  lookat: !!python/tuple
  - 0.0
  - 0.0
  - 0.0
  cam_prim_path: /OmniverseKit_Persp
  resolution: !!python/tuple
  - 1280
  - 720
  origin_type: world
  env_index: 0
  asset_name: null
sim:
  physics_prim_path: /physicsScene
  dt: 0.005
  render_interval: 4
  gravity: !!python/tuple
  - 0.0
  - 0.0
  - -9.81
  enable_scene_query_support: false
  use_fabric: true
  disable_contact_processing: true
  use_gpu_pipeline: true
  device: cuda:0
  physx:
    use_gpu: true
    solver_type: 1
    min_position_iteration_count: 1
    max_position_iteration_count: 255
    min_velocity_iteration_count: 0
    max_velocity_iteration_count: 255
    enable_ccd: false
    enable_stabilization: true
    enable_enhanced_determinism: false
    bounce_threshold_velocity: 0.5
    friction_offset_threshold: 0.04
    friction_correlation_distance: 0.025
    gpu_max_rigid_contact_count: 8388608
    gpu_max_rigid_patch_count: 163840
    gpu_found_lost_pairs_capacity: 2097152
    gpu_found_lost_aggregate_pairs_capacity: 33554432
    gpu_total_aggregate_pairs_capacity: 2097152
    gpu_collision_stack_size: 67108864
    gpu_heap_capacity: 67108864
    gpu_temp_buffer_capacity: 16777216
    gpu_max_num_partitions: 8
    gpu_max_soft_body_contacts: 1048576
    gpu_max_particle_contacts: 1048576
  physics_material:
    func: omni.isaac.lab.sim.spawners.materials.physics_materials:spawn_rigid_body_material
    static_friction: 1.0
    dynamic_friction: 1.0
    restitution: 0.0
    improve_patch_friction: true
    friction_combine_mode: multiply
    restitution_combine_mode: multiply
    compliant_contact_stiffness: 0.0
    compliant_contact_damping: 0.0
ui_window_class_type: omni.isaac.lab.envs.ui.manager_based_rl_env_window:ManagerBasedRLEnvWindow
decimation: 4
scene:
  num_envs: 4096
  env_spacing: 2.5
  lazy_sensor_update: true
  replicate_physics: true
  robot:
    class_type: omni.isaac.lab.assets.articulation.articulation:Articulation
    prim_path: /World/envs/env_.*/Robot
    spawn:
      func: omni.isaac.lab.sim.spawners.from_files.from_files:spawn_from_usd
      visible: true
      semantic_tags: null
      copy_from_source: true
      mass_props: null
      deformable_props: null
      rigid_props:
        rigid_body_enabled: null
        kinematic_enabled: null
        disable_gravity: false
        linear_damping: 0.0
        angular_damping: 0.0
        max_linear_velocity: 1000.0
        max_angular_velocity: 1000.0
        max_depenetration_velocity: 1.0
        max_contact_impulse: null
        enable_gyroscopic_forces: null
        retain_accelerations: false
        solver_position_iteration_count: null
        solver_velocity_iteration_count: null
        sleep_threshold: null
        stabilization_threshold: null
      collision_props: null
      activate_contact_sensors: true
      scale: null
      articulation_props:
        articulation_enabled: null
        enabled_self_collisions: false
        solver_position_iteration_count: 4
        solver_velocity_iteration_count: 4
        sleep_threshold: null
        stabilization_threshold: null
        fix_root_link: null
      fixed_tendons_props: null
      joint_drive_props: null
      visual_material_path: material
      visual_material: null
      usd_path: http://omniverse-content-production.s3-us-west-2.amazonaws.com/Assets/Isaac/4.0/Isaac/IsaacLab/Robots/Unitree/H1/h1_minimal.usd
      variants: null
    init_state:
      pos: !!python/tuple
      - 0.0
      - 0.0
      - 1.05
      rot: &id003 !!python/tuple
      - 1.0
      - 0.0
      - 0.0
      - 0.0
      lin_vel: &id001 !!python/tuple
      - 0.0
      - 0.0
      - 0.0
      ang_vel: *id001
      joint_pos:
        .*_hip_yaw: 0.0
        .*_hip_roll: 0.0
        .*_hip_pitch: -0.28
        .*_knee: 0.79
        .*_ankle: -0.52
        torso: 0.0
        .*_shoulder_pitch: 0.28
        .*_shoulder_roll: 0.0
        .*_shoulder_yaw: 0.0
        .*_elbow: 0.52
      joint_vel:
        .*: 0.0
    collision_group: 0
    debug_vis: false
    soft_joint_pos_limit_factor: 0.9
    actuators:
      legs:
        class_type: omni.isaac.lab.actuators.actuator_pd:ImplicitActuator
        joint_names_expr:
        - .*_hip_yaw
        - .*_hip_roll
        - .*_hip_pitch
        - .*_knee
        - torso
        effort_limit: 300
        velocity_limit: 100.0
        stiffness:
          .*_hip_yaw: 150.0
          .*_hip_roll: 150.0
          .*_hip_pitch: 200.0
          .*_knee: 200.0
          torso: 200.0
        damping:
          .*_hip_yaw: 5.0
          .*_hip_roll: 5.0
          .*_hip_pitch: 5.0
          .*_knee: 5.0
          torso: 5.0
        armature: null
        friction: null
      feet:
        class_type: omni.isaac.lab.actuators.actuator_pd:ImplicitActuator
        joint_names_expr:
        - .*_ankle
        effort_limit: 100
        velocity_limit: 100.0
        stiffness:
          .*_ankle: 20.0
        damping:
          .*_ankle: 4.0
        armature: null
        friction: null
      arms:
        class_type: omni.isaac.lab.actuators.actuator_pd:ImplicitActuator
        joint_names_expr:
        - .*_shoulder_pitch
        - .*_shoulder_roll
        - .*_shoulder_yaw
        - .*_elbow
        effort_limit: 300
        velocity_limit: 100.0
        stiffness:
          .*_shoulder_pitch: 40.0
          .*_shoulder_roll: 40.0
          .*_shoulder_yaw: 40.0
          .*_elbow: 40.0
        damping:
          .*_shoulder_pitch: 10.0
          .*_shoulder_roll: 10.0
          .*_shoulder_yaw: 10.0
          .*_elbow: 10.0
        armature: null
        friction: null
  terrain:
    class_type: omni.isaac.lab.terrains.terrain_importer:TerrainImporter
    collision_group: -1
    prim_path: /World/ground
    num_envs: 4096
    terrain_type: plane
    terrain_generator: null
    usd_path: null
    env_spacing: 2.5
    visual_material:
      func: omni.isaac.lab.sim.spawners.materials.visual_materials:spawn_from_mdl_file
      mdl_path: http://omniverse-content-production.s3-us-west-2.amazonaws.com/Assets/Isaac/4.0/Isaac/IsaacLab/Materials/TilesMarbleSpiderWhiteBrickBondHoned/TilesMarbleSpiderWhiteBrickBondHoned.mdl
      project_uvw: true
      albedo_brightness: null
      texture_scale: !!python/tuple
      - 0.25
      - 0.25
    physics_material:
      func: omni.isaac.lab.sim.spawners.materials.physics_materials:spawn_rigid_body_material
      static_friction: 1.0
      dynamic_friction: 1.0
      restitution: 0.0
      improve_patch_friction: true
      friction_combine_mode: multiply
      restitution_combine_mode: multiply
      compliant_contact_stiffness: 0.0
      compliant_contact_damping: 0.0
    max_init_terrain_level: 5
    debug_vis: false
  height_scanner: null
  contact_forces:
    class_type: omni.isaac.lab.sensors.contact_sensor.contact_sensor:ContactSensor
    prim_path: /World/envs/env_.*/Robot/.*
    update_period: 0.005
    history_length: 3
    debug_vis: false
    track_pose: false
    track_air_time: true
    force_threshold: 1.0
    filter_prim_paths_expr: []
    visualizer_cfg:
      prim_path: /Visuals/ContactSensor
      markers:
        contact:
          func: omni.isaac.lab.sim.spawners.shapes.shapes:spawn_sphere
          visible: true
          semantic_tags: null
          copy_from_source: true
          mass_props: null
          rigid_props: null
          collision_props: null
          activate_contact_sensors: false
          visual_material_path: material
          visual_material:
            func: omni.isaac.lab.sim.spawners.materials.visual_materials:spawn_preview_surface
            diffuse_color: !!python/tuple
            - 1.0
            - 0.0
            - 0.0
            emissive_color: &id002 !!python/tuple
            - 0.0
            - 0.0
            - 0.0
            roughness: 0.5
            metallic: 0.0
            opacity: 1.0
          physics_material_path: material
          physics_material: null
          radius: 0.02
        no_contact:
          func: omni.isaac.lab.sim.spawners.shapes.shapes:spawn_sphere
          visible: false
          semantic_tags: null
          copy_from_source: true
          mass_props: null
          rigid_props: null
          collision_props: null
          activate_contact_sensors: false
          visual_material_path: material
          visual_material:
            func: omni.isaac.lab.sim.spawners.materials.visual_materials:spawn_preview_surface
            diffuse_color: !!python/tuple
            - 0.0
            - 1.0
            - 0.0
            emissive_color: *id002
            roughness: 0.5
            metallic: 0.0
            opacity: 1.0
          physics_material_path: material
          physics_material: null
          radius: 0.02
  sky_light:
    class_type: {}
    prim_path: /World/skyLight
    spawn:
      func: omni.isaac.lab.sim.spawners.lights.lights:spawn_light
      visible: true
      semantic_tags: null
      copy_from_source: true
      prim_type: DomeLight
      color: !!python/tuple
      - 1.0
      - 1.0
      - 1.0
      enable_color_temperature: false
      color_temperature: 6500.0
      normalize: false
      exposure: 0.0
      intensity: 750.0
      texture_file: http://omniverse-content-production.s3-us-west-2.amazonaws.com/Assets/Isaac/4.0/Isaac/Materials/Textures/Skies/PolyHaven/kloofendal_43d_clear_puresky_4k.hdr
      texture_format: automatic
      visible_in_primary_ray: true
    init_state:
      pos: !!python/tuple
      - 0.0
      - 0.0
      - 0.0
      rot: *id003
    collision_group: 0
    debug_vis: false
observations:
  policy:
    concatenate_terms: true
    enable_corruption: true
    base_lin_vel:
      func: omni.isaac.lab.envs.mdp.observations:base_lin_vel
      params: {}
      noise:
        func: omni.isaac.lab.utils.noise.noise_model:uniform_noise
        operation: add
        n_min: -0.1
        n_max: 0.1
      clip: null
      scale: null
    base_ang_vel:
      func: omni.isaac.lab.envs.mdp.observations:base_ang_vel
      params: {}
      noise:
        func: omni.isaac.lab.utils.noise.noise_model:uniform_noise
        operation: add
        n_min: -0.2
        n_max: 0.2
      clip: null
      scale: null
    projected_gravity:
      func: omni.isaac.lab.envs.mdp.observations:projected_gravity
      params: {}
      noise:
        func: omni.isaac.lab.utils.noise.noise_model:uniform_noise
        operation: add
        n_min: -0.05
        n_max: 0.05
      clip: null
      scale: null
    velocity_commands:
      func: omni.isaac.lab.envs.mdp.observations:generated_commands
      params:
        command_name: base_velocity
      noise: null
      clip: null
      scale: null
    joint_pos:
      func: omni.isaac.lab.envs.mdp.observations:joint_pos_rel
      params: {}
      noise:
        func: omni.isaac.lab.utils.noise.noise_model:uniform_noise
        operation: add
        n_min: -0.01
        n_max: 0.01
      clip: null
      scale: null
    joint_vel:
      func: omni.isaac.lab.envs.mdp.observations:joint_vel_rel
      params: {}
      noise:
        func: omni.isaac.lab.utils.noise.noise_model:uniform_noise
        operation: add
        n_min: -1.5
        n_max: 1.5
      clip: null
      scale: null
    actions:
      func: omni.isaac.lab.envs.mdp.observations:last_action
      params: {}
      noise: null
      clip: null
      scale: null
    height_scan: null
actions:
  joint_pos:
    class_type: omni.isaac.lab.envs.mdp.actions.joint_actions:JointPositionAction
    asset_name: robot
    debug_vis: false
    joint_names:
    - .*
    scale: 0.5
    offset: 0.0
    use_default_offset: true
events:
  physics_material:
    func: omni.isaac.lab.envs.mdp.events:randomize_rigid_body_material
    params:
      asset_cfg:
        name: robot
        joint_names: null
        joint_ids: !!python/object/apply:builtins.slice
        - null
        - null
        - null
        fixed_tendon_names: null
        fixed_tendon_ids: !!python/object/apply:builtins.slice
        - null
        - null
        - null
        body_names: .*
        body_ids: !!python/object/apply:builtins.slice
        - null
        - null
        - null
        preserve_order: false
      static_friction_range: !!python/tuple
      - 0.8
      - 0.8
      dynamic_friction_range: !!python/tuple
      - 0.6
      - 0.6
      restitution_range: &id004 !!python/tuple
      - 0.0
      - 0.0
      num_buckets: 64
    mode: startup
    interval_range_s: null
    is_global_time: false
  add_base_mass: null
  base_external_force_torque:
    func: omni.isaac.lab.envs.mdp.events:apply_external_force_torque
    params:
      asset_cfg:
        name: robot
        joint_names: null
        joint_ids: !!python/object/apply:builtins.slice
        - null
        - null
        - null
        fixed_tendon_names: null
        fixed_tendon_ids: !!python/object/apply:builtins.slice
        - null
        - null
        - null
        body_names:
        - .*torso_link
        body_ids: !!python/object/apply:builtins.slice
        - null
        - null
        - null
        preserve_order: false
      force_range: *id004
      torque_range: !!python/tuple
      - -0.0
      - 0.0
    mode: reset
    interval_range_s: null
    is_global_time: false
  reset_base:
    func: omni.isaac.lab.envs.mdp.events:reset_root_state_uniform
    params:
      pose_range:
        x: &id005 !!python/tuple
        - -0.5
        - 0.5
        y: *id005
        yaw: !!python/tuple
        - -3.14
        - 3.14
      velocity_range:
        x: &id006 !!python/tuple
        - 0.0
        - 0.0
        y: *id006
        z: *id006
        roll: *id006
        pitch: *id006
        yaw: *id006
    mode: reset
    interval_range_s: null
    is_global_time: false
  reset_robot_joints:
    func: omni.isaac.lab.envs.mdp.events:reset_joints_by_scale
    params:
      position_range: !!python/tuple
      - 1.0
      - 1.0
      velocity_range: *id004
    mode: reset
    interval_range_s: null
    is_global_time: false
  push_robot: null
randomization: null
is_finite_horizon: false
episode_length_s: 20.0
rewards:
  track_lin_vel_xy_exp:
    func: omni.isaac.lab_tasks.manager_based.locomotion.velocity.mdp.rewards:track_lin_vel_xy_yaw_frame_exp
    params:
      command_name: base_velocity
      std: 0.5
    weight: 1.0
  track_ang_vel_z_exp:
    func: omni.isaac.lab_tasks.manager_based.locomotion.velocity.mdp.rewards:track_ang_vel_z_world_exp
    params:
      command_name: base_velocity
      std: 0.5
    weight: 1.0
  lin_vel_z_l2: null
  ang_vel_xy_l2:
    func: omni.isaac.lab.envs.mdp.rewards:ang_vel_xy_l2
    params: {}
    weight: -0.05
  dof_torques_l2:
    func: omni.isaac.lab.envs.mdp.rewards:joint_torques_l2
    params: {}
    weight: 0.0
  dof_acc_l2:
    func: omni.isaac.lab.envs.mdp.rewards:joint_acc_l2
    params: {}
    weight: -1.25e-07
  action_rate_l2:
    func: omni.isaac.lab.envs.mdp.rewards:action_rate_l2
    params: {}
    weight: -0.005
  feet_air_time:
    func: omni.isaac.lab_tasks.manager_based.locomotion.velocity.mdp.rewards:feet_air_time_positive_biped
    params:
      command_name: base_velocity
      sensor_cfg:
        name: contact_forces
        joint_names: null
        joint_ids: !!python/object/apply:builtins.slice
        - null
        - null
        - null
        fixed_tendon_names: null
        fixed_tendon_ids: !!python/object/apply:builtins.slice
        - null
        - null
        - null
        body_names: .*ankle_link
        body_ids: !!python/object/apply:builtins.slice
        - null
        - null
        - null
        preserve_order: false
      threshold: 0.6
    weight: 1.0
  undesired_contacts: null
  flat_orientation_l2:
    func: omni.isaac.lab.envs.mdp.rewards:flat_orientation_l2
    params: {}
    weight: -1.0
  dof_pos_limits:
    func: omni.isaac.lab.envs.mdp.rewards:joint_pos_limits
    params:
      asset_cfg:
        name: robot
        joint_names: .*_ankle
        joint_ids: !!python/object/apply:builtins.slice
        - null
        - null
        - null
        fixed_tendon_names: null
        fixed_tendon_ids: !!python/object/apply:builtins.slice
        - null
        - null
        - null
        body_names: null
        body_ids: !!python/object/apply:builtins.slice
        - null
        - null
        - null
        preserve_order: false
    weight: -1.0
  termination_penalty:
    func: omni.isaac.lab.envs.mdp.rewards:is_terminated
    params: {}
    weight: -200.0
  feet_slide:
    func: omni.isaac.lab_tasks.manager_based.locomotion.velocity.mdp.rewards:feet_slide
    params:
      sensor_cfg:
        name: contact_forces
        joint_names: null
        joint_ids: !!python/object/apply:builtins.slice
        - null
        - null
        - null
        fixed_tendon_names: null
        fixed_tendon_ids: !!python/object/apply:builtins.slice
        - null
        - null
        - null
        body_names: .*ankle_link
        body_ids: !!python/object/apply:builtins.slice
        - null
        - null
        - null
        preserve_order: false
      asset_cfg:
        name: robot
        joint_names: null
        joint_ids: !!python/object/apply:builtins.slice
        - null
        - null
        - null
        fixed_tendon_names: null
        fixed_tendon_ids: !!python/object/apply:builtins.slice
        - null
        - null
        - null
        body_names: .*ankle_link
        body_ids: !!python/object/apply:builtins.slice
        - null
        - null
        - null
        preserve_order: false
    weight: -0.25
  joint_deviation_hip:
    func: omni.isaac.lab.envs.mdp.rewards:joint_deviation_l1
    params:
      asset_cfg:
        name: robot
        joint_names:
        - .*_hip_yaw
        - .*_hip_roll
        joint_ids: !!python/object/apply:builtins.slice
        - null
        - null
        - null
        fixed_tendon_names: null
        fixed_tendon_ids: !!python/object/apply:builtins.slice
        - null
        - null
        - null
        body_names: null
        body_ids: !!python/object/apply:builtins.slice
        - null
        - null
        - null
        preserve_order: false
    weight: -0.2
  joint_deviation_arms:
    func: omni.isaac.lab.envs.mdp.rewards:joint_deviation_l1
    params:
      asset_cfg:
        name: robot
        joint_names:
        - .*_shoulder_.*
        - .*_elbow
        joint_ids: !!python/object/apply:builtins.slice
        - null
        - null
        - null
        fixed_tendon_names: null
        fixed_tendon_ids: !!python/object/apply:builtins.slice
        - null
        - null
        - null
        body_names: null
        body_ids: !!python/object/apply:builtins.slice
        - null
        - null
        - null
        preserve_order: false
    weight: -0.2
  joint_deviation_torso:
    func: omni.isaac.lab.envs.mdp.rewards:joint_deviation_l1
    params:
      asset_cfg:
        name: robot
        joint_names: torso
        joint_ids: !!python/object/apply:builtins.slice
        - null
        - null
        - null
        fixed_tendon_names: null
        fixed_tendon_ids: !!python/object/apply:builtins.slice
        - null
        - null
        - null
        body_names: null
        body_ids: !!python/object/apply:builtins.slice
        - null
        - null
        - null
        preserve_order: false
    weight: -0.1
terminations:
  time_out:
    func: omni.isaac.lab.envs.mdp.terminations:time_out
    params: {}
    time_out: true
  base_contact:
    func: omni.isaac.lab.envs.mdp.terminations:illegal_contact
    params:
      sensor_cfg:
        name: contact_forces
        joint_names: null
        joint_ids: !!python/object/apply:builtins.slice
        - null
        - null
        - null
        fixed_tendon_names: null
        fixed_tendon_ids: !!python/object/apply:builtins.slice
        - null
        - null
        - null
        body_names:
        - .*torso_link
        body_ids: !!python/object/apply:builtins.slice
        - null
        - null
        - null
        preserve_order: false
      threshold: 1.0
    time_out: false
curriculum:
  terrain_levels: null
commands:
  base_velocity:
    class_type: omni.isaac.lab.envs.mdp.commands.velocity_command:UniformVelocityCommand
    resampling_time_range: !!python/tuple
    - 10.0
    - 10.0
    debug_vis: true
    asset_name: robot
    heading_command: true
    heading_control_stiffness: 0.5
    rel_standing_envs: 0.02
    rel_heading_envs: 1.0
    ranges:
      lin_vel_x: !!python/tuple
      - 0.0
      - 1.0
      lin_vel_y: *id006
      ang_vel_z: !!python/tuple
      - -1.0
      - 1.0
      heading: !!python/tuple
      - -3.141592653589793
      - 3.141592653589793
