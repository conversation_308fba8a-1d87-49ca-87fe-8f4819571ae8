#!/bin/bash

set -e

# 设置变量
ISAAC_PYTHON_PATH=/opt/isaac-sim/kit/python
ROS_DISTRO=humble
ROS_ROOT=/opt/src/ros_ws
ISAAC_ROS_ROOT=/opt/src/isaac_ros_src
ROS_PYTHON_VERSION=3
DEBIAN_FRONTEND=noninteractive
ROS_INSTALL_PATH=/opt/ros/${ROS_DISTRO}
ISAAC_ROS_INSTALL_PATH=/opt/isaac_ros

cd ${ISAAC_ROS_ROOT}

# # Removing MoveIt packages from the internal ROS Python 3.11 library build as it uses standard interfaces already built above.
# # This is to ensure that the internal build is as minimal as possible. 
# # For the user facing MoveIt interface workflow, this package should be built with the rest of the workspace uisng the external ROS installation.
rm -rf ${ISAAC_ROS_ROOT}/moveit

source ${ROS_INSTALL_PATH}/setup.bash
colcon build --cmake-args \
    "-DPython3_EXECUTABLE=${ISAAC_PYTHON_PATH}/bin/python3.11" \
    "-DPYTHON_EXECUTABLE=${ISAAC_PYTHON_PATH}/bin/python3.11" \
    "-DPYTHON_INCLUDE_DIR=${ISAAC_PYTHON_PATH}/include/python3.11" \
    "-DPYTHON_LIBRARY=${ISAAC_PYTHON_PATH}/lib/libpython3.11.so" \
    --merge-install \
    --install-base $ISAAC_ROS_INSTALL_PATH