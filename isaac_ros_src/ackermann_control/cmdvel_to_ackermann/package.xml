<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format3.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>cmdvel_to_a<PERSON>mann</name>
  <version>0.1.0</version>
  <description>This package converts command velocity (Twist Msg) to ackermann messages (AckermannDriveStamped Msg)</description>
  <maintainer email="<EMAIL>"><PERSON></maintainer>

  <license>
    SPDX-FileCopyrightText: Copyright (c) 2025 NVIDIA CORPORATION &amp; AFFILIATES. All rights reserved.
    SPDX-License-Identifier: Apache-2.0

    Licensed under the Apache License, Version 2.0 (the "License");
    you may not use this file except in compliance with the License.
    You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.
  </license>  
  <url type="Documentation">https://docs.isaacsim.omniverse.nvidia.com/5.0.0/index.html</url>
  <url type="Forums">https://forums.developer.nvidia.com/c/omniverse/simulation/69</url>
  
  <buildtool_depend>ament_cmake</buildtool_depend>

  <build_depend>ackermann_msgs</build_depend>
  <build_depend>geometry_msgs</build_depend>
  <build_depend>launch</build_depend>

  <export>
    <build_type>ament_cmake</build_type>
  </export>
</package>
