<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format3.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>carter_navigation</name>
  <version>0.1.0</version>
  <description>
    The carter_navigation package
  </description>

  <maintainer email="<EMAIL>"><PERSON></maintainer>

  <license>
    SPDX-FileCopyrightText: Copyright (c) 2025 NVIDIA CORPORATION &amp; AFFILIATES. All rights reserved.
    SPDX-License-Identifier: Apache-2.0

    Licensed under the Apache License, Version 2.0 (the "License");
    you may not use this file except in compliance with the License.
    You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.
  </license>
  <url type="Documentation">https://docs.isaacsim.omniverse.nvidia.com/5.0.0/index.html</url>
  <url type="Forums">https://forums.developer.nvidia.com/c/omniverse/simulation/69</url>

  <buildtool_depend>ament_cmake</buildtool_depend>

  <build_depend>launch</build_depend>
  <build_depend>launch_ros</build_depend>

  <build_depend>joint_state_publisher</build_depend>

  <build_depend>rviz2</build_depend>

  <build_depend>navigation2</build_depend>
  <build_depend>nav2_amcl</build_depend>
  <build_depend>nav2_bringup</build_depend>
  <build_depend>nav2_bt_navigator</build_depend>
  <build_depend>nav2_costmap_2d</build_depend>
  <build_depend>nav2_core</build_depend>
  <build_depend>nav2_dwb_controller</build_depend>
  <build_depend>nav2_lifecycle_manager</build_depend>
  <build_depend>nav2_map_server</build_depend>
  <build_depend>nav2_behaviors</build_depend>
  <build_depend>nav2_planner</build_depend>
  <build_depend>nav2_msgs</build_depend>
  <build_depend>nav2_navfn_planner</build_depend>
  <build_depend>nav2_rviz_plugins</build_depend>
  <build_depend>nav2_behavior_tree</build_depend>
  <build_depend>nav2_util</build_depend>
  <build_depend>nav2_voxel_grid</build_depend>
  <build_depend>nav2_controller</build_depend>
  <build_depend>nav2_waypoint_follower</build_depend>
  <build_depend>pointcloud_to_laserscan</build_depend>


  <export>
    <build_type>ament_cmake</build_type>
  </export>
</package>
