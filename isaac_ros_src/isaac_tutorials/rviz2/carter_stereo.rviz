Panels:
  - Class: rviz_common/Displays
    Help Height: 138
    Name: Displays
    Property Tree Widget:
      Expanded:
        - /Global Options1
        - /Status1
        - /TF1
        - /Odometry1/Topic1
        - /Left Camera - RGB1
        - /Right Camera - RGB1/Topic1
      Splitter Ratio: 0.5
    Tree Height: 718
  - Class: rviz_common/Selection
    Name: Selection
  - Class: rviz_common/Tool Properties
    Expanded:
      - /2D Goal Pose1
      - /Publish Point1
    Name: Tool Properties
    Splitter Ratio: 0.5886790156364441
  - Class: rviz_common/Views
    Expanded:
      - /Current View1
    Name: Views
    Splitter Ratio: 0.5
Visualization Manager:
  Class: ""
  Displays:
    - Alpha: 0.5
      Cell Size: 1
      Class: rviz_default_plugins/Grid
      Color: 160; 160; 164
      Enabled: true
      Line Style:
        Line Width: 0.029999999329447746
        Value: Lines
      Name: Grid
      Normal Cell Count: 0
      Offset:
        X: 0
        Y: 0
        Z: 0
      Plane: XY
      Plane Cell Count: 10
      Reference Frame: <Fixed Frame>
      Value: true
    - Class: rviz_default_plugins/TF
      Enabled: true
      Frame Timeout: 15
      Frames:
        All Enabled: true
        back_fisheye_camera:
          Value: true
        back_fisheye_camera_optical:
          Value: true
        back_stereo_camera:
          Value: true
        back_stereo_camera_imu:
          Value: true
        back_stereo_camera_left:
          Value: true
        back_stereo_camera_left_optical:
          Value: true
        back_stereo_camera_right:
          Value: true
        back_stereo_camera_right_optical:
          Value: true
        base_link:
          Value: true
        caster_swivel_left:
          Value: true
        caster_swivel_right:
          Value: true
        caster_wheel_left:
          Value: true
        caster_wheel_right:
          Value: true
        chassis_imu:
          Value: true
        front_2d_lidar:
          Value: true
        front_3d_lidar:
          Value: true
        front_fisheye_camera:
          Value: true
        front_fisheye_camera_optical:
          Value: true
        front_stereo_camera:
          Value: true
        front_stereo_camera_imu:
          Value: true
        front_stereo_camera_left:
          Value: true
        front_stereo_camera_left_optical:
          Value: true
        front_stereo_camera_right:
          Value: true
        front_stereo_camera_right_optical:
          Value: true
        left_fisheye_camera:
          Value: true
        left_fisheye_camera_optical:
          Value: true
        left_stereo_camera:
          Value: true
        left_stereo_camera_imu:
          Value: true
        left_stereo_camera_left:
          Value: true
        left_stereo_camera_left_optical:
          Value: true
        left_stereo_camera_right:
          Value: true
        left_stereo_camera_right_optical:
          Value: true
        nova_carter:
          Value: true
        nova_carter_caster_frame_base:
          Value: true
        odom:
          Value: true
        rear_2d_lidar:
          Value: true
        right_fisheye_camera:
          Value: true
        right_fisheye_camera_optical:
          Value: true
        right_stereo_camera:
          Value: true
        right_stereo_camera_imu:
          Value: true
        right_stereo_camera_left:
          Value: true
        right_stereo_camera_left_optical:
          Value: true
        right_stereo_camera_right:
          Value: true
        right_stereo_camera_right_optical:
          Value: true
        wheel_left:
          Value: true
        wheel_right:
          Value: true
      Marker Scale: 1
      Name: TF
      Show Arrows: true
      Show Axes: true
      Show Names: false
      Tree:
        odom:
          base_link:
            back_fisheye_camera:
              {}
            back_fisheye_camera_optical:
              {}
            back_stereo_camera:
              {}
            back_stereo_camera_imu:
              {}
            back_stereo_camera_left:
              {}
            back_stereo_camera_left_optical:
              {}
            back_stereo_camera_right:
              {}
            back_stereo_camera_right_optical:
              {}
            chassis_imu:
              {}
            front_2d_lidar:
              {}
            front_3d_lidar:
              {}
            front_fisheye_camera:
              {}
            front_fisheye_camera_optical:
              {}
            front_stereo_camera:
              {}
            front_stereo_camera_imu:
              {}
            front_stereo_camera_left:
              {}
            front_stereo_camera_left_optical:
              {}
            front_stereo_camera_right:
              {}
            front_stereo_camera_right_optical:
              {}
            left_fisheye_camera:
              {}
            left_fisheye_camera_optical:
              {}
            left_stereo_camera:
              {}
            left_stereo_camera_imu:
              {}
            left_stereo_camera_left:
              {}
            left_stereo_camera_left_optical:
              {}
            left_stereo_camera_right:
              {}
            left_stereo_camera_right_optical:
              {}
            nova_carter:
              caster_swivel_left:
                {}
              caster_swivel_right:
                {}
              caster_wheel_left:
                {}
              caster_wheel_right:
                {}
              nova_carter_caster_frame_base:
                {}
              wheel_left:
                {}
              wheel_right:
                {}
            rear_2d_lidar:
              {}
            right_fisheye_camera:
              {}
            right_fisheye_camera_optical:
              {}
            right_stereo_camera:
              {}
            right_stereo_camera_imu:
              {}
            right_stereo_camera_left:
              {}
            right_stereo_camera_left_optical:
              {}
            right_stereo_camera_right:
              {}
            right_stereo_camera_right_optical:
              {}
      Update Interval: 0
      Value: true
    - Angle Tolerance: 0.10000000149011612
      Class: rviz_default_plugins/Odometry
      Covariance:
        Orientation:
          Alpha: 0.5
          Color: 255; 255; 127
          Color Style: Unique
          Frame: Local
          Offset: 1
          Scale: 1
          Value: true
        Position:
          Alpha: 0.30000001192092896
          Color: 204; 51; 204
          Scale: 1
          Value: true
        Value: true
      Enabled: true
      Keep: 100
      Name: Odometry
      Position Tolerance: 0.10000000149011612
      Shape:
        Alpha: 1
        Axes Length: 1
        Axes Radius: 0.10000000149011612
        Color: 255; 25; 0
        Head Length: 0.30000001192092896
        Head Radius: 0.10000000149011612
        Shaft Length: 1
        Shaft Radius: 0.05000000074505806
        Value: Arrow
      Topic:
        Depth: 5
        Durability Policy: Volatile
        Filter size: 10
        History Policy: Keep Last
        Reliability Policy: Reliable
        Value: odom
      Value: true
    - Class: rviz_default_plugins/Image
      Enabled: true
      Max Value: 1
      Median window: 5
      Min Value: 0
      Name: Left Camera - RGB
      Normalize Range: true
      Topic:
        Depth: 5
        Durability Policy: Volatile
        History Policy: Keep Last
        Reliability Policy: Best Effort
        Value: /front_stereo_camera/left/image_raw
      Value: true
    - Class: rviz_default_plugins/Image
      Enabled: true
      Max Value: 1
      Median window: 5
      Min Value: 0
      Name: Right Camera - RGB
      Normalize Range: true
      Topic:
        Depth: 5
        Durability Policy: Volatile
        History Policy: Keep Last
        Reliability Policy: Best Effort
        Value: /front_stereo_camera/right/image_raw
      Value: true
    - Alpha: 1
      Autocompute Intensity Bounds: true
      Autocompute Value Bounds:
        Max Value: 10
        Min Value: -10
        Value: true
      Axis: Z
      Channel Name: intensity
      Class: rviz_default_plugins/PointCloud2
      Color: 255; 255; 255
      Color Transformer: Intensity
      Decay Time: 0
      Enabled: true
      Invert Rainbow: false
      Max Color: 255; 255; 255
      Max Intensity: 4096
      Min Color: 0; 0; 0
      Min Intensity: 0
      Name: PointCloud2
      Position Transformer: XYZ
      Selectable: true
      Size (Pixels): 3
      Size (m): 0.009999999776482582
      Style: Flat Squares
      Topic:
        Depth: 5
        Durability Policy: Volatile
        Filter size: 10
        History Policy: Keep Last
        Reliability Policy: Best Effort
        Value: /front_3d_lidar/lidar_points
      Use Fixed Frame: true
      Use rainbow: true
      Value: true
  Enabled: true
  Global Options:
    Background Color: 48; 48; 48
    Fixed Frame: odom
    Frame Rate: 30
  Name: root
  Tools:
    - Class: rviz_default_plugins/Interact
      Hide Inactive Objects: true
    - Class: rviz_default_plugins/MoveCamera
    - Class: rviz_default_plugins/Select
    - Class: rviz_default_plugins/FocusCamera
    - Class: rviz_default_plugins/Measure
      Line color: 128; 128; 0
    - Class: rviz_default_plugins/SetInitialPose
      Covariance x: 0.25
      Covariance y: 0.25
      Covariance yaw: 0.06853891909122467
      Topic:
        Depth: 5
        Durability Policy: Volatile
        History Policy: Keep Last
        Reliability Policy: Reliable
        Value: /initialpose
    - Class: rviz_default_plugins/SetGoal
      Topic:
        Depth: 5
        Durability Policy: Volatile
        History Policy: Keep Last
        Reliability Policy: Reliable
        Value: /goal_pose
    - Class: rviz_default_plugins/PublishPoint
      Single click: true
      Topic:
        Depth: 5
        Durability Policy: Volatile
        History Policy: Keep Last
        Reliability Policy: Reliable
        Value: /clicked_point
  Transformation:
    Current:
      Class: rviz_default_plugins/TF
  Value: true
  Views:
    Current:
      Class: rviz_default_plugins/Orbit
      Distance: 7.74399995803833
      Enable Stereo Rendering:
        Stereo Eye Separation: 0.05999999865889549
        Stereo Focal Distance: 1
        Swap Stereo Eyes: false
        Value: false
      Focal Point:
        X: 0.7320740818977356
        Y: 0.9904739856719971
        Z: 1.7632932662963867
      Focal Shape Fixed Size: true
      Focal Shape Size: 0.05000000074505806
      Invert Z Axis: false
      Name: Current View
      Near Clip Distance: 0.009999999776482582
      Pitch: 0.7803976535797119
      Target Frame: <Fixed Frame>
      Value: Orbit (rviz)
      Yaw: 1.0203975439071655
    Saved: ~
Window Geometry:
  Displays:
    collapsed: false
  Height: 1007
  Hide Left Dock: false
  Hide Right Dock: true
  Left Camera - RGB:
    collapsed: false
  QMainWindow State: 000000ff00000000fd0000000400000000000001f700000395fc020000000dfb0000001200530065006c0065006300740069006f006e00000001e10000009b0000005c00fffffffb0000001e0054006f006f006c002000500072006f007000650072007400690065007302000001ed000001df00000185000000a3fb000000120056006900650077007300200054006f006f02000001df000002110000018500000122fb000000200054006f006f006c002000500072006f0070006500720074006900650073003203000002880000011d000002210000017afb000000100044006900730070006c006100790073010000003d00000395000000c900fffffffb0000002000730065006c0065006300740069006f006e00200062007500660066006500720200000138000000aa0000023a00000294fb00000014005700690064006500530074006500720065006f02000000e6000000d2000003ee0000030bfb0000000c004b0069006e0065006300740200000186000001060000030c00000261fb0000002800520069006700680074002000430061006d0065007200610020002d002000440065007000740068020000128b00000218000002390000017ffb00000026004c006500660074002000430061006d0065007200610020002d002000440065007000740068020000102e000002180000023c00000181fb00000022004c006500660074002000430061006d0065007200610020002d0020005200470042030000080a0000018d000001f30000011efb0000002400520069006700680074002000430061006d0065007200610020002d002000520047004203000008320000039d000001f30000011efb0000000a0049006d006100670065030000236c0000040e0000023e0000017e000000010000015f000003a4fc0200000003fb0000001e0054006f006f006c002000500072006f00700065007200740069006500730100000041000000780000000000000000fb0000000a00560069006500770073000000003d000003a4000000a400fffffffb0000001200530065006c0065006300740069006f006e010000025a000000b200000000000000000000000200000490000000a9fc0100000001fb0000000a00560069006500770073030000004e00000080000002e10000019700000003000004420000003efc0100000002fb0000000800540069006d00650100000000000004420000000000000000fb0000000800540069006d00650100000000000004500000000000000000000006950000039500000004000000040000000800000008fc0000000100000002000000010000000a0054006f006f006c00730100000000ffffffff0000000000000000
  Right Camera - RGB:
    collapsed: false
  Selection:
    collapsed: false
  Tool Properties:
    collapsed: false
  Views:
    collapsed: true
  Width: 2194
  X: 458
  Y: 217
