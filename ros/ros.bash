#!/bin/bash

set -e

USER=${1:-"ubt"}

# 创建工作目录
sudo mkdir -p /opt/src && sudo chown -R ${USER}:${USER} /opt/src
cd /opt/src

# 设置变量
ISAAC_PYTHON_PATH=/opt/isaac-sim/kit/python
ROS_DISTRO=humble
ROS_ROOT=/opt/src/ros_ws
ISAAC_ROS_ROOT=/opt/src/isaac_ros_ws
ROS_PYTHON_VERSION=3
DEBIAN_FRONTEND=noninteractive
ROS_INSTALL_PATH=/opt/ros/${ROS_DISTRO}

export PATH="${ISAAC_PYTHON_PATH}/bin:$PATH"

# # 安装基础依赖
# sudo apt-get update
# sudo apt-get install -y --no-install-recommends \
#      git cmake build-essential curl wget gnupg2 lsb-release software-properties-common locales

# sudo locale-gen en_US en_US.UTF-8
# sudo update-locale LC_ALL=en_US.UTF-8 LANG=en_US.UTF-8

git clone http://github.com/ros/rosdistro.git --depth 1
cp rosdistro/ros.asc /tmp/ros.asc && rm -rf rosdistro
sudo apt-key add /tmp/ros.asc
sudo sh -c "echo 'deb [arch=$(dpkg --print-architecture)] http://mirrors.ustc.edu.cn/ros2/ubuntu $(lsb_release -cs) main' > /etc/apt/sources.list.d/ros2-latest.list"

sudo apt update && sudo apt install -y --no-install-recommends \
    ros-dev-tools
# # rosidl_generator_c 依赖
# sudo apt update
# sudo apt install -y \
#     pkg-config \
#     python3-yaml \
#     cmake-extras
# # OMPL 相关 Boost 库
# sudo apt install -y \
#     libboost-all-dev \
#     libboost-dev \
#     libboost-filesystem-dev \
#     libboost-program-options-dev \
#     libboost-system-dev \
#     libboost-thread-dev \
#     libboost-serialization-dev \
#     libboost-date-time-dev \
#     libboost-regex-dev \
#     libboost-python-dev \
#     libfmt-dev
# # geometric_shapes 及其他包依赖
# sudo apt install -y \
#     libqhull-dev \
#     libassimp-dev \
#     liboctomap-dev \
#     libconsole-bridge-dev \
#     libfcl-dev
# # Eigen3
# sudo apt install -y libeigen3-dev
# # X11 和图形依赖（OGRE/RViz）
# sudo apt install -y \
#     libx11-dev \
#     libxaw7-dev \
#     libxrandr-dev \
#     libgl1-mesa-dev \
#     libglu1-mesa-dev \
#     libglew-dev \
#     libgles2-mesa-dev \
#     libopengl-dev
#     # libfreetype-dev \
#     # libfreetype6-dev \
#     # libfontconfig1-dev
# # Qt5 及 RViz 依赖
# sudo apt install -y \
#     qtbase5-dev \
#     qtchooser \
#     qt5-qmake \
#     qtbase5-dev-tools \
#     libqt5core5a \
#     libqt5gui5 \
#     libqt5opengl5 \
#     libqt5widgets5 \
#     libxcursor-dev \
#     libxinerama-dev \
#     libxi-dev \
#     libyaml-cpp-dev \
#     libassimp-dev \
#     libzzip-dev \
#     freeglut3-dev \
#     libogre-1.9-dev \
#     libpng-dev \
#     libjpeg-dev \
#     python3-pyqt5.qtwebengine

# sudo apt install -y \
#     ros-dev-tools \
#     libbullet-dev \
#     libasio-dev \
#     libtinyxml2-dev \
#     libcunit1-dev \
#     libacl1-dev

# python3.11 -m pip install empy==3.3.4
# python3 -m pip install -U \
#   argcomplete \
#   flake8-blind-except \
#   flake8-builtins \
#   flake8-class-newline \
#   flake8-comprehensions \
#   flake8-deprecated \
#   flake8-docstrings \
#   flake8-import-order \
#   flake8-quotes \
#   pytest-repeat \
#   pytest-rerunfailures \
#   pytest \
#   lark

# python3.11 -m pip install numpy pybind11 PyYAML
# python3.11 -m pip install "pybind11[global]"
python3.11 -m pip install rosinstall_generator

# 模拟 rosdep
export ROSDISTRO_INDEX_URL=https://mirrors.ustc.edu.cn/rosdistro/index-v4.yaml
sudo mkdir -p /etc/ros/rosdep/sources.list.d
sudo curl -o /etc/ros/rosdep/sources.list.d/20-default.list https://mirrors.ustc.edu.cn/rosdistro/rosdep/sources.list.d/20-default.list
sudo sed -i 's#raw.githubusercontent.com/ros/rosdistro/master#mirrors.ustc.edu.cn/rosdistro#g' /etc/ros/rosdep/sources.list.d/20-default.list
rosdep update

mkdir -p ${ROS_ROOT}/src
cd ${ROS_ROOT}
rosinstall_generator --deps --rosdistro ${ROS_DISTRO} rosidl_runtime_c rcutils rcl rmw tf2 tf2_msgs common_interfaces geometry_msgs nav_msgs std_msgs rosgraph_msgs sensor_msgs vision_msgs rclpy ros2topic ros2pkg ros2doctor ros2run ros2node ros_environment ackermann_msgs example_interfaces tf2_ros_py tf2_ros > ros2.${ROS_DISTRO}.${ROS_PKG}.rosinstall
sed -i 's|https://github\.com|https://gh-proxy.rd.ubtrobot.com|g' ros2.${ROS_DISTRO}.${ROS_PKG}.rosinstall
cat ros2.${ROS_DISTRO}.${ROS_PKG}.rosinstall
sleep 1
for i in {1..5}; do
    vcs import --skip-existing src < ros2.${ROS_DISTRO}.${ROS_PKG}.rosinstall && break
    # 遍历所有文件夹，把空文件夹删除
    find src -type d -empty -delete
    echo "vcs import failed, retrying ($i/5)..."
    sleep 1
done

# Patch rclpy 以支持 Python 3.11
find ${ROS_ROOT}/src -name rclpy -type d | xargs -I{} bash -c 'if [ -f {}/CMakeLists.txt ]; then \
    echo "Patching {}/CMakeLists.txt"; \
    sed -i "s/include_directories(\${PYTHON_INCLUDE_DIRS})/include_directories(\/opt\/isaac-sim\/kit\/python\/include\/python3.11)/" {}/CMakeLists.txt; \
    sed -i "s/\${PYTHON_LIBRARY}/python3.11/" {}/CMakeLists.txt; \
    fi'

export PYTHONPATH=${ISAAC_PYTHON_PATH}/lib/python3.11/site-packages
    
git config --global url."http://gh-proxy.rd.ubtrobot.com".insteadOf https://github.com
sed -i 's|URL https://github.com/gabime/spdlog/archive/v1.8.2.tar.gz|GIT_REPOSITORY https://gh-proxy.rd.ubtrobot.com/gabime/spdlog.git|; s|URL_MD5 22518fb28d4be66c92a703c67d99b1d1|GIT_TAG v1.8.2|' ${ROS_ROOT}/src/spdlog_vendor/CMakeLists.txt

# colcon 构建
cd ${ROS_ROOT}
colcon build --cmake-args \
    "-DPython3_EXECUTABLE=${ISAAC_PYTHON_PATH}/bin/python3.11" \
    "-DPYTHON_EXECUTABLE=${ISAAC_PYTHON_PATH}/bin/python3.11" \
    "-DPYTHON_INCLUDE_DIR=${ISAAC_PYTHON_PATH}/include/python3.11" \
    "-DPYTHON_LIBRARY=${ISAAC_PYTHON_PATH}/lib/libpython3.11.so" \
    --merge-install \
    --install-base $ROS_INSTALL_PATH

# 兼容非 20.04 系统
cp /usr/lib/x86_64-linux-gnu/libtinyxml2.so* ${ROS_ROOT}/install/lib/ || true
cp /usr/lib/x86_64-linux-gnu/libssl.so* ${ROS_ROOT}/install/lib/ || true
cp /usr/lib/x86_64-linux-gnu/libcrypto.so* ${ROS_ROOT}/install/lib/ || true