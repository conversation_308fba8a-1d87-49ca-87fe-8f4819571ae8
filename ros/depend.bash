#!/bin/bash

set -e

# apt 
# 安装基础依赖
sudo apt-get update
sudo apt-get install -y --no-install-recommends \
     git cmake build-essential curl wget gnupg2 lsb-release software-properties-common locales

sudo locale-gen en_US en_US.UTF-8
sudo update-locale LC_ALL=en_US.UTF-8 LANG=en_US.UTF-8

# rosidl_generator_c 依赖
sudo apt update
sudo apt install -y \
    pkg-config \
    python3-yaml \
    cmake-extras
# OMPL 相关 Boost 库
sudo apt install -y \
    libboost-all-dev \
    libboost-dev \
    libboost-filesystem-dev \
    libboost-program-options-dev \
    libboost-system-dev \
    libboost-thread-dev \
    libboost-serialization-dev \
    libboost-date-time-dev \
    libboost-regex-dev \
    libboost-python-dev \
    libfmt-dev
# geometric_shapes 及其他包依赖
sudo apt install -y \
    libqhull-dev \
    libassimp-dev \
    liboctomap-dev \
    libconsole-bridge-dev \
    libfcl-dev
# Eigen3
sudo apt install -y libeigen3-dev
# X11 和图形依赖（OGRE/RViz）
sudo apt install -y \
    libx11-dev \
    libxaw7-dev \
    libxrandr-dev \
    libgl1-mesa-dev \
    libglu1-mesa-dev \
    libglew-dev \
    libgles2-mesa-dev \
    libopengl-dev
    # libfreetype-dev \
    # libfreetype6-dev \
    # libfontconfig1-dev
# Qt5 及 RViz 依赖
sudo apt install -y \
    qtbase5-dev \
    qtchooser \
    qt5-qmake \
    qtbase5-dev-tools \
    libqt5core5a \
    libqt5gui5 \
    libqt5opengl5 \
    libqt5widgets5 \
    libxcursor-dev \
    libxinerama-dev \
    libxi-dev \
    libyaml-cpp-dev \
    libassimp-dev \
    libzzip-dev \
    freeglut3-dev \
    libogre-1.9-dev \
    libpng-dev \
    libjpeg-dev \
    python3-pyqt5.qtwebengine

sudo apt install -y \
    libbullet-dev \
    libasio-dev \
    libtinyxml2-dev \
    libcunit1-dev \
    libacl1-dev

# pip
ISAAC_PYTHON_PATH=/opt/isaac-sim/kit/python
export PATH="${ISAAC_PYTHON_PATH}/bin:$PATH"
python3.11 -m pip install empy==3.3.4
python3 -m pip install -U \
  argcomplete \
  flake8-blind-except \
  flake8-builtins \
  flake8-class-newline \
  flake8-comprehensions \
  flake8-deprecated \
  flake8-docstrings \
  flake8-import-order \
  flake8-quotes \
  pytest-repeat \
  pytest-rerunfailures \
  pytest \
  lark

python3.11 -m pip install numpy pybind11 PyYAML
python3.11 -m pip install "pybind11[global]"